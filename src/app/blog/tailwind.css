@import "tailwindcss";
@plugin "@tailwindcss/typography";

/* Theme */
:root {
  --background: hsl(41 100% 95%);
  --foreground: hsl(41 5% 10%);
  --card: hsl(41 50% 90%);
  --card-foreground: hsl(41 5% 15%);
  --popover: hsl(41 100% 95%);
  --popover-foreground: hsl(41 100% 10%);
  --primary: hsl(41 59% 60%);
  --primary-foreground: hsl(0 0% 100%);
  --secondary: hsl(41 30% 70%);
  --secondary-foreground: hsl(0 0% 0%);
  --muted: hsl(3 30% 85%);
  --muted-foreground: hsl(41 5% 40%);
  --accent: hsl(3 30% 80%);
  --accent-foreground: hsl(41 5% 15%);
  --destructive: hsl(0 100% 50%);
  --destructive-foreground: hsl(41 5% 90%);
  --border: hsl(41 30% 53%);
  --input: hsl(41 30% 50%);
  --ring: hsl(41 59% 60%);
  --radius: 0.75rem;
}
.dark {
  --background: hsl(41 50% 10%);
  --foreground: hsl(41 5% 90%);
  --card: hsl(41 50% 10%);
  --card-foreground: hsl(41 5% 90%);
  --popover: hsl(41 50% 5%);
  --popover-foreground: hsl(41 5% 90%);
  --primary: hsl(41 59% 60%);
  --primary-foreground: hsl(0 0% 100%);
  --secondary: hsl(41 30% 20%);
  --secondary-foreground: hsl(0 0% 100%);
  --muted: hsl(3 30% 25%);
  --muted-foreground: hsl(41 5% 60%);
  --accent: hsl(3 30% 25%);
  --accent-foreground: hsl(41 5% 90%);
  --destructive: hsl(0 100% 50%);
  --destructive-foreground: hsl(41 5% 90%);
  --border: hsl(41 30% 50%);
  --input: hsl(41 30% 50%);
  --ring: hsl(41 59% 60%);
  --radius: 0.75rem;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
}

/* Typography */
@layer base {
  /* ===== Override default Tailwind Typography styles ===== */
  .app-prose {
    @apply prose;

    * {
      margin-top: 0!important;
      &:last-child {
        margin-bottom: 0!important;
      }
    }
    
    h1,
    h2,
    h3,
    h4,
    th {
      @apply mb-6 text-foreground;
    }

    h3 {
      @apply italic;
    }

    p,
    strong,
    ol,
    ul,
    figcaption,
    table,
    code {
      @apply text-foreground;
    }

    a {
      @apply break-words text-foreground decoration-dashed underline-offset-4 hover:text-accent focus-visible:no-underline;
    }

    ul {
      @apply overflow-x-clip;
    }

    li {
      @apply marker:text-accent;
    }

    hr {
      @apply border-border;
    }

    img {
      @apply w-full max-h-[350px] mx-auto border border-border object-cover rounded-md;
    }

    figcaption {
      @apply opacity-75;
    }

    table {
      th,
      td {
        @apply border border-border !p-2;
      }

      th {
        @apply py-1.5;
      }

      code {
        @apply break-all sm:break-normal;
      }
    }

    code {
      @apply rounded bg-background/75 p-1 break-words text-foreground before:content-none after:content-none;
      > span {
        @apply whitespace-pre-wrap;
      }
    }

    .astro-code code {
      @apply flex-[1_0_100%] bg-inherit p-0;
    }

    blockquote {
      @apply border-s-accent/80 break-words opacity-80;
    }

    details {
      @apply inline-block cursor-pointer text-foreground select-none [&_p]:hidden [&_ul]:!my-0;
    }

    summary {
      @apply focus-visible:no-underline focus-visible:outline-2 focus-visible:outline-offset-1 focus-visible:outline-accent focus-visible:outline-dashed;
    }

    pre {
      @apply focus-visible:border-transparent focus-visible:outline-2 focus-visible:outline-accent focus-visible:outline-dashed;
    }
  }

  /* Astro */
  /* ===== Code Blocks & Syntax Highlighting ===== */
  /* .astro-code {
    @apply flex border bg-(--shiki-light-bg) text-(--shiki-light) outline-border [&_span]:text-(--shiki-light);
  }
  html[data-theme="dark"] .astro-code {
    @apply bg-(--shiki-dark-bg) text-(--shiki-dark) [&_span]:text-(--shiki-dark);
  } */
  /* Styles for Shiki transformers */
  /* https://shiki.style/packages/transformers */
  /* .astro-code {
    .line.diff.add {
      @apply relative inline-block w-full bg-green-400/20 before:absolute before:-left-3 before:text-green-500 before:content-['+'];
    }
    .line.diff.remove {
      @apply relative inline-block w-full bg-red-500/20 before:absolute before:-left-3 before:text-red-500 before:content-['-'];
    }
    .line.highlighted {
      @apply inline-block w-full bg-slate-400/20;
    }
    .highlighted-word {
      @apply rounded-sm border border-border px-0.5 py-px;
    }
  } */

  /* Cursor */
  /* ===== Code Blocks & Syntax Highlighting ===== */
  /* rehype-pretty-code 输出结构支持 */
  [data-rehype-pretty-code-fragment] pre {
    @apply rounded-lg p-4 overflow-x-auto border border-border;
  }
  [data-rehype-pretty-code-title] {
    &+ [data-rehype-pretty-code-fragment] {
      pre {
        @apply rounded-tl-none rounded-tr-none;
      }
    }
  }
  [data-rehype-pretty-code-title] {
    @apply block rounded-t-lg border border-b-0 border-border bg-card px-3 py-2 text-xs text-foreground/80;
  }
  pre code { @apply bg-transparent p-0; }

  /* Shiki 主题变量适配（与 article 类名中的 prose-pre:bg-(--shiki-*) 对应）*/
  .prose pre { @apply bg-(--shiki-light-bg) text-(--shiki-light); }
  .dark .prose pre { @apply bg-(--shiki-dark-bg) text-(--shiki-dark); }

  /* 可选：diff/高亮等辅助类（rehype-pretty-code/shiki 常用类名）*/
  /* .line.diff.add { @apply relative inline-block w-full bg-green-400/20 before:absolute before:-left-3 before:text-green-500 before:content-["+"]; }
  .line.diff.remove { @apply relative inline-block w-full bg-red-500/20 before:absolute before:-left-3 before:text-red-500 before:content-["-"]; }
  .line.highlighted { @apply inline-block w-full bg-slate-400/20; }
  .highlighted-word { @apply rounded-sm border border-border px-0.5 py-px; } */
}

/* Lines numbers */

code[data-line-numbers] {
  counter-reset: line;
}

code[data-line-numbers] > [data-line]::before {
  counter-increment: line;
  content: counter(line);

  /* Other styling */
  display: inline-block;
  width: 0.75rem;
  margin-right: 2rem;
  text-align: right;
  color: gray;
}

code[data-line-numbers-max-digits="2"] > [data-line]::before {
  width: 1.25rem;
}

code[data-line-numbers-max-digits="3"] > [data-line]::before {
  width: 1.75rem;
}

code[data-line-numbers-max-digits="4"] > [data-line]::before {
  width: 2.25rem;
}

/* Theme */
code[data-theme*=" "],
code[data-theme*=" "] span {
  color: var(--shiki-light);
  background-color: var(--shiki-light-bg);
}

@media (prefers-color-scheme: dark) {
  code[data-theme*=" "],
  code[data-theme*=" "] span {
    color: var(--shiki-dark);
    background-color: var(--shiki-dark-bg);
  }
}
