---
title: 'JavaScript 入门全景指南：语法与示例（含 Markdown 样式演示）'
publishedAt: '2024-08-10'
summary: '一文掌握 JavaScript 入门路径，并穷举常见 Markdown/MDX 语法以便在博客中验证样式呈现效果。'
---

# JavaScript 入门全景指南

这篇文章以“如何入门 JavaScript”为主线，同时尽可能覆盖常见 Markdown/MDX 语法，便于你在博客中全面校验排版与样式。

- 目标读者：零基础到初学者
- 阅读耗时：约 20～30 分钟
- 学习产出：完成环境准备、理解核心语法、能写出一个小功能

> 提示：本页用于样式测试，内容力求自洽但以覆盖面为先。

---

## 目录

- [JavaScript 入门全景指南](#javascript-入门全景指南)
  - [目录](#目录)
  - [准备环境](#准备环境)
  - [核心语法](#核心语法)
    - [变量与常量](#变量与常量)
    - [数据类型](#数据类型)
    - [运算与比较](#运算与比较)
    - [流程控制](#流程控制)
    - [函数与作用域](#函数与作用域)
    - [数组与对象](#数组与对象)
  - [DOM 与事件](#dom-与事件)
  - [模块与打包](#模块与打包)
  - [实践：待办清单](#实践待办清单)
  - [附录：Markdown 语法演示](#附录markdown-语法演示)
    - [标题层级](#标题层级)
- [H1 标题示例](#h1-标题示例)
  - [H2 标题示例](#h2-标题示例)
    - [H3 标题示例](#h3-标题示例)
      - [H4 标题示例](#h4-标题示例)
        - [H5 标题示例](#h5-标题示例)
          - [H6 标题示例](#h6-标题示例)
    - [强调与行内元素](#强调与行内元素)
    - [列表](#列表)
    - [表格](#表格)
    - [代码块](#代码块)
    - [引用与提示](#引用与提示)
    - [分隔线与换行](#分隔线与换行)
    - [链接与图片](#链接与图片)
    - [折叠与明细](#折叠与明细)
  - [结束语](#结束语)

---

## 准备环境

1. 安装 Node.js（包含 npm）。推荐通过 nvm 安装并切换版本：

```bash
# 安装 nvm（不同系统命令可能不同）
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.7/install.sh | bash

# 安装 LTS 版本并使用
nvm install --lts
nvm use --lts

# 验证
node -v
npm -v
```

2. 选择编辑器。推荐 VS Code，并安装 ESLint、Prettier、TypeScript 相关扩展。
3. 新建项目目录并初始化：

```bash
mkdir js-quickstart && cd js-quickstart
npm init -y
npm pkg set type="module"
```

> 说明：`type="module"` 让 Node 使用 ESM 模块语法（`import/export`）。

---

## 核心语法

### 变量与常量

- 使用 `let` 声明可变变量，`const` 声明常量。
- 建议尽量使用 `const`，仅在确需变更时用 `let`。

```js
const siteName = 'One Web Blog'
let counter = 0
counter += 1
```

> 不推荐使用 `var`，其函数作用域和变量提升行为容易引发困惑。

### 数据类型

- 基本类型：`string`、`number`、`boolean`、`null`、`undefined`、`symbol`、`bigint`
- 引用类型：`object`（对象、数组、函数、Date、Map、Set 等）

```js
const title = 'Hello'
const version = 1
const isActive = true
const list = [1, 2, 3]
const user = { id: 1, name: 'Jin' }
```

### 运算与比较

- 数学运算：`+ - * / % **`
- 比较运算：`===`、`!==` 优先，避免 `==` 带来的隐式转换

```js
2 ** 3 === 8 // true
'3' == 3     // true（不推荐）
'3' === 3    // false（推荐）
```

### 流程控制

```js
const score = 85
if (score >= 90) {
  console.log('优秀')
} else if (score >= 60) {
  console.log('及格')
} else {
  console.log('不及格')
}

for (const n of [1, 2, 3]) console.log(n)
```

### 函数与作用域

- 推荐使用具名函数与箭头函数，保持单一职责。

```js
function sum(a, b) {
  return a + b
}

const multiply = (a, b) => a * b
```

> 闭包：函数捕获其外层作用域中的变量。

```js
function makeCounter() {
  let value = 0
  return () => ++value
}

const next = makeCounter()
next() // 1
next() // 2
```

### 数组与对象

- 常用方法：`map`、`filter`、`reduce`、`find`、`some`、`every`
- 扩展与解构：`...`、对象与数组解构

```js
const nums = [1, 2, 3]
const doubled = nums.map(n => n * 2) // [2, 4, 6]

const base = { a: 1, b: 2 }
const ext = { ...base, b: 3, c: 4 } // { a:1, b:3, c:4 }

const { a, c } = ext
const [first] = nums
```

---

## DOM 与事件

在浏览器中，可以通过 DOM API 操作页面并监听事件：

```html
<button id="btn">Click</button>
<script type="module">
  const btn = document.getElementById('btn')
  btn.addEventListener('click', () => alert('Clicked'))
</script>
```

> 生产中建议使用框架（如 React/Vue），统一状态与更新流程。

---

## 模块与打包

- ESM：`import { fn } from './utils.js'`
- CommonJS：`const pkg = require('pkg')`（不建议在新项目使用）

打包工具常见选项：Vite、Webpack、esbuild、Rollup。示例（Vite）：

```bash
npm create vite@latest my-app -- --template react
cd my-app
npm i
npm run dev
```

---

## 实践：待办清单

目标：实现一个最小可用的待办清单（添加、切换完成、统计）。

```js
function createTodoApp() {
  /** @type {{ id:number; text:string; done:boolean }[]} */
  let todos = []
  let nextId = 1

  function addTodo(text) {
    if (!text?.trim()) return
    todos = [...todos, { id: nextId++, text: text.trim(), done: false }]
  }

  function toggleTodo(id) {
    todos = todos.map(t => (t.id === id ? { ...t, done: !t.done } : t))
  }

  function stats() {
    const total = todos.length
    const done = todos.filter(t => t.done).length
    return { total, done, undone: total - done }
  }

  return { addTodo, toggleTodo, stats, get todos() { return todos } }
}

const app = createTodoApp()
app.addTodo('学习变量与常量')
app.addTodo('实现 toggleTodo')
app.toggleTodo(1)
console.log(app.todos, app.stats())
```

---

## 附录：Markdown 语法演示

> 本节专为样式覆盖，推荐通读并观察页面效果。

### 标题层级

# H1 标题示例

## H2 标题示例

### H3 标题示例

#### H4 标题示例

##### H5 标题示例

###### H6 标题示例

### 强调与行内元素

- 普通文本、`行内代码`、以及包含特殊字符的转义：\*星号\*、\_下划线\_
- **加粗**、*斜体*、***粗斜体***、~~删除线~~
- 高亮（HTML）：<mark>重要提示</mark>

### 列表

- 无序列表
  - 二级项
    - 三级项
- 支持嵌套

1. 有序列表项 A
2. 有序列表项 B
   1. 子项 B.1
   2. 子项 B.2

- 任务清单（GFM）
  - [x] 搭建环境
  - [ ] 学习语法
  - [ ] 完成小项目

### 表格

| 名称 | 类型     | 说明             |
| :--- | :------- | :--------------- |
| name | string   | 用户名           |
| age  | number   | 年龄             |
| done | boolean  | 是否完成         |

### 代码块

```js {1, 3-5} title="..." file="demo.js"
// JavaScript 示例
export function greet(name) {
  // 这行会被高亮显示
  return `Hello, ${name}!` // {* important *}
  console.log({+ "Added line" +})
  console.log({- "Removed line" -})
  const message = "highlight this {very important phrase}"
}
```

```ts
// TypeScript 示例
export interface User { id: number; name: string }
export function getName(u: User): string { return u.name }
```

```bash
# Shell 示例
pnpm i
pnpm dev
```

```diff
- const mode = 'development'
+ const mode = process.env.NODE_ENV || 'development'
```

### 引用与提示

> 单段落引用：学习编程最重要的是持续练习。
>
> 多段落引用支持。

> ℹ️ 侧注：可以结合引用与列表
> - 要点一
> - 要点二

### 分隔线与换行

---

使用两个空格结尾进行换行：第一行  
第二行（应换行）。

### 链接与图片

- 行内链接：[MDN JavaScript](https://developer.mozilla.org/docs/Web/JavaScript)
- 带标题链接：[ECMAScript 规范](https://tc39.es/ecma262/ "ECMA-262")
- 引用式链接：参见[本仓库 README][repo]

[repo]: https://github.com/

- 图片（站点 public 资源）：

![Next 标志](/next.svg)

- 图片（外链）：

![外链示例](https://upload.wikimedia.org/wikipedia/commons/3/3b/Javascript_Logo.png)

### 折叠与明细

<details>
  <summary>Click to expand</summary>
  <p>这里可以放额外的解释、长代码或截图说明。</p>
</details>

---

## 结束语

你已了解 JavaScript 入门路径与关键语法，并完成常见 Markdown 语法的视觉回归。接下来可尝试：

- 用数组/对象组合出更复杂的数据结构
- 把待办清单迁移到浏览器中，使用本地存储持久化
- 选择一个构建工具或框架，完成组件化重构

