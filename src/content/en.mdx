---
title: 'A Complete Beginner’s Guide to JavaScript: Syntax and Examples (with Markdown Showcase)'
publishedAt: '2024-08-10'
summary: 'Learn the essentials of getting started with JavaScript while exhaustively demonstrating common Markdown/MDX syntax for style verification in this blog.'
---

# A Complete Beginner’s Guide to JavaScript

This article uses “how to get started with JavaScript” as the main thread while covering as many common Markdown/MDX features as possible, so you can fully validate typography and styles in your blog.

- Target audience: absolute beginners to new learners
- Reading time: about 20–30 minutes
- Outcome: environment setup, core syntax understanding, and a small working feature

> Note: This page is for style testing. Content aims for coherence but prioritizes coverage.

---

## Table of Contents

- [A Complete Beginner’s Guide to JavaScript](#a-complete-beginners-guide-to-javascript)
  - [Table of Contents](#table-of-contents)
  - [Prepare the Environment](#prepare-the-environment)
  - [Core Syntax](#core-syntax)
    - [Variables and Constants](#variables-and-constants)
    - [Data Types](#data-types)
    - [Operations and Comparisons](#operations-and-comparisons)
    - [Control Flow](#control-flow)
    - [Functions and Scope](#functions-and-scope)
    - [Arrays and Objects](#arrays-and-objects)
  - [DOM and Events](#dom-and-events)
  - [Modules and Bundling](#modules-and-bundling)
  - [Hands-on: Todo List](#hands-on-todo-list)
  - [Appendix: Markdown Showcase](#appendix-markdown-showcase)
    - [Heading Levels](#heading-levels)
  - [H2 Heading Example](#h2-heading-example)
    - [H3 Heading Example](#h3-heading-example)
      - [H4 Heading Example](#h4-heading-example)
        - [H5 Heading Example](#h5-heading-example)
          - [H6 Heading Example](#h6-heading-example)
    - [Emphasis and Inline Elements](#emphasis-and-inline-elements)
    - [Lists](#lists)
    - [Tables](#tables)
    - [Code Blocks](#code-blocks)
    - [Blockquotes and Admonitions](#blockquotes-and-admonitions)
    - [Rules and Line Breaks](#rules-and-line-breaks)
    - [Links and Images](#links-and-images)
    - [Details and Disclosure](#details-and-disclosure)
  - [tango](#tango)
  - [Closing](#closing)

---

## Prepare the Environment

1. Install Node.js (includes npm). It’s recommended to use nvm to install and switch versions:

```bash
# Install nvm (commands may vary by OS)
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.7/install.sh | bash

# Install and use the LTS version
nvm install --lts
nvm use --lts

# Verify
node -v
npm -v
```

2. Choose an editor. VS Code is recommended. Install ESLint, Prettier, and TypeScript related extensions.
3. Create a new project directory and initialize it:

```bash
mkdir js-quickstart && cd js-quickstart
npm init -y
npm pkg set type="module"
```

> Explanation: `type="module"` enables ESM module syntax (`import/export`) in Node.

---

## Core Syntax

### Variables and Constants

- Use `let` for mutable variables and `const` for constants.
- Prefer `const` whenever possible; use `let` only when mutation is required.

```js
const siteName = 'One Web Blog'
let counter = 0
counter += 1
```

> Avoid `var` due to function scoping and hoisting surprises.

### Data Types

- Primitives: `string`, `number`, `boolean`, `null`, `undefined`, `symbol`, `bigint`
- Reference types: `object` (objects, arrays, functions, Date, Map, Set, etc.)

```js
const title = 'Hello'
const version = 1
const isActive = true
const list = [1, 2, 3]
const user = { id: 1, name: 'Jin' }
```

### Operations and Comparisons

- Arithmetic: `+ - * / % **`
- Comparison: prefer `===` and `!==`; avoid `==` implicit coercion

```js
2 ** 3 === 8 // true
'3' == 3     // true (not recommended)
'3' === 3    // false (recommended)
```

### Control Flow

```js
const score = 85
if (score >= 90) {
  console.log('Excellent')
} else if (score >= 60) {
  console.log('Pass')
} else {
  console.log('Fail')
}

for (const n of [1, 2, 3]) console.log(n)
```

### Functions and Scope

- Prefer named functions and arrow functions with single responsibility.

```js
function sum(a, b) {
  return a + b
}

const multiply = (a, b) => a * b
```

> Closures: a function captures variables from its outer scope.

```js
function makeCounter() {
  let value = 0
  return () => ++value
}

const next = makeCounter()
next() // 1
next() // 2
```

### Arrays and Objects

- Common methods: `map`, `filter`, `reduce`, `find`, `some`, `every`
- Spreading and destructuring: `...`, object/array destructuring

```js
const nums = [1, 2, 3]
const doubled = nums.map(n => n * 2) // [2, 4, 6]

const base = { a: 1, b: 2 }
const ext = { ...base, b: 3, c: 4 } // { a:1, b:3, c:4 }

const { a, c } = ext
const [first] = nums
```

---

## DOM and Events

In the browser, you can manipulate the page and listen to events via the DOM API:

```html
<button id="btn">Click</button>
<script type="module">
  const btn = document.getElementById('btn')
  btn.addEventListener('click', () => alert('Clicked'))
</script>
```

> In production, consider a framework (e.g., React/Vue) to manage state and updates consistently.

---

## Modules and Bundling

- ESM: `import { fn } from './utils.js'`
- CommonJS: `const pkg = require('pkg')` (not recommended for new projects)

Common bundlers: Vite, Webpack, esbuild, Rollup. Example (Vite):

```bash
npm create vite@latest my-app -- --template react
cd my-app
npm i
npm run dev
```

---

## Hands-on: Todo List

Goal: implement a minimal todo list (add, toggle, and stats).

```js
function createTodoApp() {
  /** @type {{ id:number; text:string; done:boolean }[]} */
  let todos = []
  let nextId = 1

  function addTodo(text) {
    if (!text?.trim()) return
    todos = [...todos, { id: nextId++, text: text.trim(), done: false }]
  }

  function toggleTodo(id) {
    todos = todos.map(t => (t.id === id ? { ...t, done: !t.done } : t))
  }

  function stats() {
    const total = todos.length
    const done = todos.filter(t => t.done).length
    return { total, done, undone: total - done }
  }

  return { addTodo, toggleTodo, stats, get todos() { return todos } }
}

const app = createTodoApp()
app.addTodo('Learn variables and constants')
app.addTodo('Implement toggleTodo')
app.toggleTodo(1)
console.log(app.todos, app.stats())
```

---

## Appendix: Markdown Showcase

> This section is for coverage. Read through and observe the visual result.

### Heading Levels

## H2 Heading Example

### H3 Heading Example

#### H4 Heading Example

##### H5 Heading Example

###### H6 Heading Example

### Emphasis and Inline Elements

- Plain text, `inline code`, and escaped specials: \*asterisk\*, \_underscore\_
- **Bold**, *Italic*, ***BoldItalic***, ~~Strikethrough~~
- Highlight (HTML): <mark>Important</mark>

### Lists

- Unordered list
  - Level 2
    - Level 3
- Nested supported

1. Ordered A
2. Ordered B
   1. Sub B.1
   2. Sub B.2

- Task list (GFM)
  - [x] Setup environment
  - [ ] Learn syntax
  - [ ] Finish a small project

### Tables

| Name | Type    | Description        |
| :--- | :------ | :----------------- |
| name | string  | User name          |
| age  | number  | Age                |
| done | boolean | Completion status  |

### Code Blocks

```js
// JavaScript example
export function greet(name) {
  return `Hello, ${name}!`
}
```

```ts
// TypeScript example
export interface User { id: number; name: string }
export function getName(u: User): string { return u.name }
```

```bash
# Shell example
pnpm i
pnpm dev
```

```diff
- const mode = 'development'
+ const mode = process.env.NODE_ENV || 'development'
```

### Blockquotes and Admonitions

> Single-paragraph quote: The key to learning to code is consistent practice.
>
> Multi-paragraph quotes are supported.

> ℹ️ Side note: combine quotes with lists
> - Point 1
> - Point 2

### Rules and Line Breaks

---

Use two trailing spaces for a line break: First line  
Second line (should break).

### Links and Images

- Inline link: [MDN JavaScript](https://developer.mozilla.org/docs/Web/JavaScript)
- Titled link: [ECMAScript Spec](https://tc39.es/ecma262/ "ECMA-262")
- Reference link: see [Repo README][repo]

[repo]: https://github.com/

- Image (site public asset):

![Next Logo](/next.svg)

- External image:

![External Example](https://upload.wikimedia.org/wikipedia/commons/3/3b/Javascript_Logo.png)

### Details and Disclosure


## tango

target content

---

## Closing

You now understand the basic path and key syntax of JavaScript and have visually verified common Markdown patterns. Next steps:

- Combine arrays/objects to model more complex data
- Port the todo list to the browser and persist via localStorage
- Pick a bundler or framework and refactor into components
